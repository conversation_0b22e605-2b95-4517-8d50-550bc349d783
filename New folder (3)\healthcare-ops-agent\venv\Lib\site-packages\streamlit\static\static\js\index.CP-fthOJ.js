import{n as Vt,r as gt,j as lt,F as $t}from"./index.C1NIn1Y2.js";/*! @license DOMPurify 3.2.6 | (c) Cure<PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:At,setPrototypeOf:ct,isFrozen:qt,getPrototypeOf:Kt,getOwnPropertyDescriptor:Zt}=Object;let{freeze:S,seal:y,create:ht}=Object,{apply:we,construct:xe}=typeof Reflect<"u"&&Reflect;S||(S=function(n){return n});y||(y=function(n){return n});we||(we=function(n,s,l){return n.apply(s,l)});xe||(xe=function(n,s){return new n(...s)});const le=R(Array.prototype.forEach),Jt=R(Array.prototype.lastIndexOf),ft=R(Array.prototype.pop),$=R(Array.prototype.push),Qt=R(Array.prototype.splice),fe=R(String.prototype.toLowerCase),De=R(String.prototype.toString),ut=R(String.prototype.match),q=R(String.prototype.replace),en=R(String.prototype.indexOf),tn=R(String.prototype.trim),L=R(Object.prototype.hasOwnProperty),h=R(RegExp.prototype.test),K=nn(TypeError);function R(a){return function(n){n instanceof RegExp&&(n.lastIndex=0);for(var s=arguments.length,l=new Array(s>1?s-1:0),T=1;T<s;T++)l[T-1]=arguments[T];return we(a,n,l)}}function nn(a){return function(){for(var n=arguments.length,s=new Array(n),l=0;l<n;l++)s[l]=arguments[l];return xe(a,s)}}function r(a,n){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:fe;ct&&ct(a,null);let l=n.length;for(;l--;){let T=n[l];if(typeof T=="string"){const D=s(T);D!==T&&(qt(n)||(n[l]=D),T=D)}a[T]=!0}return a}function on(a){for(let n=0;n<a.length;n++)L(a,n)||(a[n]=null);return a}function C(a){const n=ht(null);for(const[s,l]of At(a))L(a,s)&&(Array.isArray(l)?n[s]=on(l):l&&typeof l=="object"&&l.constructor===Object?n[s]=C(l):n[s]=l);return n}function Z(a,n){for(;a!==null;){const l=Zt(a,n);if(l){if(l.get)return R(l.get);if(typeof l.value=="function")return R(l.value)}a=Kt(a)}function s(){return null}return s}const mt=S(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Ne=S(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ie=S(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),an=S(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Me=S(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),rn=S(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),pt=S(["#text"]),dt=S(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Ce=S(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Tt=S(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),ce=S(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),sn=y(/\{\{[\w\W]*|[\w\W]*\}\}/gm),ln=y(/<%[\w\W]*|[\w\W]*%>/gm),cn=y(/\$\{[\w\W]*/gm),fn=y(/^data-[\-\w.\u00B7-\uFFFF]+$/),un=y(/^aria-[\-\w]+$/),St=y(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),mn=y(/^(?:\w+script|data):/i),pn=y(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Rt=y(/^html$/i),dn=y(/^[a-z][.\w]*(-[.\w]+)+$/i);var _t=Object.freeze({__proto__:null,ARIA_ATTR:un,ATTR_WHITESPACE:pn,CUSTOM_ELEMENT:dn,DATA_ATTR:fn,DOCTYPE_NAME:Rt,ERB_EXPR:ln,IS_ALLOWED_URI:St,IS_SCRIPT_OR_DATA:mn,MUSTACHE_EXPR:sn,TMPLIT_EXPR:cn});const J={element:1,text:3,progressingInstruction:7,comment:8,document:9},Tn=function(){return typeof window>"u"?null:window},_n=function(n,s){if(typeof n!="object"||typeof n.createPolicy!="function")return null;let l=null;const T="data-tt-policy-suffix";s&&s.hasAttribute(T)&&(l=s.getAttribute(T));const D="dompurify"+(l?"#"+l:"");try{return n.createPolicy(D,{createHTML(x){return x},createScriptURL(x){return x}})}catch{return console.warn("TrustedTypes policy "+D+" could not be created."),null}},Et=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function Ot(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Tn();const n=i=>Ot(i);if(n.version="3.2.6",n.removed=[],!a||!a.document||a.document.nodeType!==J.document||!a.Element)return n.isSupported=!1,n;let{document:s}=a;const l=s,T=l.currentScript,{DocumentFragment:D,HTMLTemplateElement:x,Node:ue,Element:ve,NodeFilter:G,NamedNodeMap:yt=a.NamedNodeMap||a.MozNamedAttrMap,HTMLFormElement:Lt,DOMParser:bt,trustedTypes:Q}=a,W=ve.prototype,Dt=Z(W,"cloneNode"),Nt=Z(W,"remove"),It=Z(W,"nextSibling"),Mt=Z(W,"childNodes"),ee=Z(W,"parentNode");if(typeof x=="function"){const i=s.createElement("template");i.content&&i.content.ownerDocument&&(s=i.content.ownerDocument)}let g,B="";const{implementation:me,createNodeIterator:Ct,createDocumentFragment:wt,getElementsByTagName:xt}=s,{importNode:Pt}=l;let A=Et();n.isSupported=typeof At=="function"&&typeof ee=="function"&&me&&me.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:pe,ERB_EXPR:de,TMPLIT_EXPR:Te,DATA_ATTR:kt,ARIA_ATTR:vt,IS_SCRIPT_OR_DATA:Ut,ATTR_WHITESPACE:Ue,CUSTOM_ELEMENT:Ft}=_t;let{IS_ALLOWED_URI:Fe}=_t,m=null;const He=r({},[...mt,...Ne,...Ie,...Me,...pt]);let d=null;const ze=r({},[...dt,...Ce,...Tt,...ce]);let f=Object.seal(ht(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Y=null,_e=null,Ge=!0,Ee=!0,We=!1,Be=!0,P=!1,te=!0,w=!1,ge=!1,Ae=!1,k=!1,ne=!1,oe=!1,Ye=!0,Xe=!1;const Ht="user-content-";let he=!0,X=!1,v={},U=null;const je=r({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Ve=null;const $e=r({},["audio","video","img","source","image","track"]);let Se=null;const qe=r({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ie="http://www.w3.org/1998/Math/MathML",ae="http://www.w3.org/2000/svg",N="http://www.w3.org/1999/xhtml";let F=N,Re=!1,Oe=null;const zt=r({},[ie,ae,N],De);let re=r({},["mi","mo","mn","ms","mtext"]),se=r({},["annotation-xml"]);const Gt=r({},["title","style","font","a","script"]);let j=null;const Wt=["application/xhtml+xml","text/html"],Bt="text/html";let p=null,H=null;const Yt=s.createElement("form"),Ke=function(e){return e instanceof RegExp||e instanceof Function},ye=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(H&&H===e)){if((!e||typeof e!="object")&&(e={}),e=C(e),j=Wt.indexOf(e.PARSER_MEDIA_TYPE)===-1?Bt:e.PARSER_MEDIA_TYPE,p=j==="application/xhtml+xml"?De:fe,m=L(e,"ALLOWED_TAGS")?r({},e.ALLOWED_TAGS,p):He,d=L(e,"ALLOWED_ATTR")?r({},e.ALLOWED_ATTR,p):ze,Oe=L(e,"ALLOWED_NAMESPACES")?r({},e.ALLOWED_NAMESPACES,De):zt,Se=L(e,"ADD_URI_SAFE_ATTR")?r(C(qe),e.ADD_URI_SAFE_ATTR,p):qe,Ve=L(e,"ADD_DATA_URI_TAGS")?r(C($e),e.ADD_DATA_URI_TAGS,p):$e,U=L(e,"FORBID_CONTENTS")?r({},e.FORBID_CONTENTS,p):je,Y=L(e,"FORBID_TAGS")?r({},e.FORBID_TAGS,p):C({}),_e=L(e,"FORBID_ATTR")?r({},e.FORBID_ATTR,p):C({}),v=L(e,"USE_PROFILES")?e.USE_PROFILES:!1,Ge=e.ALLOW_ARIA_ATTR!==!1,Ee=e.ALLOW_DATA_ATTR!==!1,We=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Be=e.ALLOW_SELF_CLOSE_IN_ATTR!==!1,P=e.SAFE_FOR_TEMPLATES||!1,te=e.SAFE_FOR_XML!==!1,w=e.WHOLE_DOCUMENT||!1,k=e.RETURN_DOM||!1,ne=e.RETURN_DOM_FRAGMENT||!1,oe=e.RETURN_TRUSTED_TYPE||!1,Ae=e.FORCE_BODY||!1,Ye=e.SANITIZE_DOM!==!1,Xe=e.SANITIZE_NAMED_PROPS||!1,he=e.KEEP_CONTENT!==!1,X=e.IN_PLACE||!1,Fe=e.ALLOWED_URI_REGEXP||St,F=e.NAMESPACE||N,re=e.MATHML_TEXT_INTEGRATION_POINTS||re,se=e.HTML_INTEGRATION_POINTS||se,f=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&Ke(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(f.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&Ke(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(f.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(f.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),P&&(Ee=!1),ne&&(k=!0),v&&(m=r({},pt),d=[],v.html===!0&&(r(m,mt),r(d,dt)),v.svg===!0&&(r(m,Ne),r(d,Ce),r(d,ce)),v.svgFilters===!0&&(r(m,Ie),r(d,Ce),r(d,ce)),v.mathMl===!0&&(r(m,Me),r(d,Tt),r(d,ce))),e.ADD_TAGS&&(m===He&&(m=C(m)),r(m,e.ADD_TAGS,p)),e.ADD_ATTR&&(d===ze&&(d=C(d)),r(d,e.ADD_ATTR,p)),e.ADD_URI_SAFE_ATTR&&r(Se,e.ADD_URI_SAFE_ATTR,p),e.FORBID_CONTENTS&&(U===je&&(U=C(U)),r(U,e.FORBID_CONTENTS,p)),he&&(m["#text"]=!0),w&&r(m,["html","head","body"]),m.table&&(r(m,["tbody"]),delete Y.tbody),e.TRUSTED_TYPES_POLICY){if(typeof e.TRUSTED_TYPES_POLICY.createHTML!="function")throw K('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof e.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw K('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');g=e.TRUSTED_TYPES_POLICY,B=g.createHTML("")}else g===void 0&&(g=_n(Q,T)),g!==null&&typeof B=="string"&&(B=g.createHTML(""));S&&S(e),H=e}},Ze=r({},[...Ne,...Ie,...an]),Je=r({},[...Me,...rn]),Xt=function(e){let t=ee(e);(!t||!t.tagName)&&(t={namespaceURI:F,tagName:"template"});const o=fe(e.tagName),c=fe(t.tagName);return Oe[e.namespaceURI]?e.namespaceURI===ae?t.namespaceURI===N?o==="svg":t.namespaceURI===ie?o==="svg"&&(c==="annotation-xml"||re[c]):!!Ze[o]:e.namespaceURI===ie?t.namespaceURI===N?o==="math":t.namespaceURI===ae?o==="math"&&se[c]:!!Je[o]:e.namespaceURI===N?t.namespaceURI===ae&&!se[c]||t.namespaceURI===ie&&!re[c]?!1:!Je[o]&&(Gt[o]||!Ze[o]):!!(j==="application/xhtml+xml"&&Oe[e.namespaceURI]):!1},b=function(e){$(n.removed,{element:e});try{ee(e).removeChild(e)}catch{Nt(e)}},z=function(e,t){try{$(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch{$(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),e==="is")if(k||ne)try{b(t)}catch{}else try{t.setAttribute(e,"")}catch{}},Qe=function(e){let t=null,o=null;if(Ae)e="<remove></remove>"+e;else{const u=ut(e,/^[\r\n\t ]+/);o=u&&u[0]}j==="application/xhtml+xml"&&F===N&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const c=g?g.createHTML(e):e;if(F===N)try{t=new bt().parseFromString(c,j)}catch{}if(!t||!t.documentElement){t=me.createDocument(F,"template",null);try{t.documentElement.innerHTML=Re?B:c}catch{}}const _=t.body||t.documentElement;return e&&o&&_.insertBefore(s.createTextNode(o),_.childNodes[0]||null),F===N?xt.call(t,w?"html":"body")[0]:w?t.documentElement:_},et=function(e){return Ct.call(e.ownerDocument||e,e,G.SHOW_ELEMENT|G.SHOW_COMMENT|G.SHOW_TEXT|G.SHOW_PROCESSING_INSTRUCTION|G.SHOW_CDATA_SECTION,null)},Le=function(e){return e instanceof Lt&&(typeof e.nodeName!="string"||typeof e.textContent!="string"||typeof e.removeChild!="function"||!(e.attributes instanceof yt)||typeof e.removeAttribute!="function"||typeof e.setAttribute!="function"||typeof e.namespaceURI!="string"||typeof e.insertBefore!="function"||typeof e.hasChildNodes!="function")},tt=function(e){return typeof ue=="function"&&e instanceof ue};function I(i,e,t){le(i,o=>{o.call(n,e,t,H)})}const nt=function(e){let t=null;if(I(A.beforeSanitizeElements,e,null),Le(e))return b(e),!0;const o=p(e.nodeName);if(I(A.uponSanitizeElement,e,{tagName:o,allowedTags:m}),te&&e.hasChildNodes()&&!tt(e.firstElementChild)&&h(/<[/\w!]/g,e.innerHTML)&&h(/<[/\w!]/g,e.textContent)||e.nodeType===J.progressingInstruction||te&&e.nodeType===J.comment&&h(/<[/\w]/g,e.data))return b(e),!0;if(!m[o]||Y[o]){if(!Y[o]&&it(o)&&(f.tagNameCheck instanceof RegExp&&h(f.tagNameCheck,o)||f.tagNameCheck instanceof Function&&f.tagNameCheck(o)))return!1;if(he&&!U[o]){const c=ee(e)||e.parentNode,_=Mt(e)||e.childNodes;if(_&&c){const u=_.length;for(let O=u-1;O>=0;--O){const M=Dt(_[O],!0);M.__removalCount=(e.__removalCount||0)+1,c.insertBefore(M,It(e))}}}return b(e),!0}return e instanceof ve&&!Xt(e)||(o==="noscript"||o==="noembed"||o==="noframes")&&h(/<\/no(script|embed|frames)/i,e.innerHTML)?(b(e),!0):(P&&e.nodeType===J.text&&(t=e.textContent,le([pe,de,Te],c=>{t=q(t,c," ")}),e.textContent!==t&&($(n.removed,{element:e.cloneNode()}),e.textContent=t)),I(A.afterSanitizeElements,e,null),!1)},ot=function(e,t,o){if(Ye&&(t==="id"||t==="name")&&(o in s||o in Yt))return!1;if(!(Ee&&!_e[t]&&h(kt,t))){if(!(Ge&&h(vt,t))){if(!d[t]||_e[t]){if(!(it(e)&&(f.tagNameCheck instanceof RegExp&&h(f.tagNameCheck,e)||f.tagNameCheck instanceof Function&&f.tagNameCheck(e))&&(f.attributeNameCheck instanceof RegExp&&h(f.attributeNameCheck,t)||f.attributeNameCheck instanceof Function&&f.attributeNameCheck(t))||t==="is"&&f.allowCustomizedBuiltInElements&&(f.tagNameCheck instanceof RegExp&&h(f.tagNameCheck,o)||f.tagNameCheck instanceof Function&&f.tagNameCheck(o))))return!1}else if(!Se[t]){if(!h(Fe,q(o,Ue,""))){if(!((t==="src"||t==="xlink:href"||t==="href")&&e!=="script"&&en(o,"data:")===0&&Ve[e])){if(!(We&&!h(Ut,q(o,Ue,"")))){if(o)return!1}}}}}}return!0},it=function(e){return e!=="annotation-xml"&&ut(e,Ft)},at=function(e){I(A.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||Le(e))return;const o={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:d,forceKeepAttr:void 0};let c=t.length;for(;c--;){const _=t[c],{name:u,namespaceURI:O,value:M}=_,V=p(u),be=M;let E=u==="value"?be:tn(be);if(o.attrName=V,o.attrValue=E,o.keepAttr=!0,o.forceKeepAttr=void 0,I(A.uponSanitizeAttribute,e,o),E=o.attrValue,Xe&&(V==="id"||V==="name")&&(z(u,e),E=Ht+E),te&&h(/((--!?|])>)|<\/(style|title)/i,E)){z(u,e);continue}if(o.forceKeepAttr)continue;if(!o.keepAttr){z(u,e);continue}if(!Be&&h(/\/>/i,E)){z(u,e);continue}P&&le([pe,de,Te],st=>{E=q(E,st," ")});const rt=p(e.nodeName);if(!ot(rt,V,E)){z(u,e);continue}if(g&&typeof Q=="object"&&typeof Q.getAttributeType=="function"&&!O)switch(Q.getAttributeType(rt,V)){case"TrustedHTML":{E=g.createHTML(E);break}case"TrustedScriptURL":{E=g.createScriptURL(E);break}}if(E!==be)try{O?e.setAttributeNS(O,u,E):e.setAttribute(u,E),Le(e)?b(e):ft(n.removed)}catch{z(u,e)}}I(A.afterSanitizeAttributes,e,null)},jt=function i(e){let t=null;const o=et(e);for(I(A.beforeSanitizeShadowDOM,e,null);t=o.nextNode();)I(A.uponSanitizeShadowNode,t,null),nt(t),at(t),t.content instanceof D&&i(t.content);I(A.afterSanitizeShadowDOM,e,null)};return n.sanitize=function(i){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t=null,o=null,c=null,_=null;if(Re=!i,Re&&(i="<!-->"),typeof i!="string"&&!tt(i))if(typeof i.toString=="function"){if(i=i.toString(),typeof i!="string")throw K("dirty is not a string, aborting")}else throw K("toString is not a function");if(!n.isSupported)return i;if(ge||ye(e),n.removed=[],typeof i=="string"&&(X=!1),X){if(i.nodeName){const M=p(i.nodeName);if(!m[M]||Y[M])throw K("root node is forbidden and cannot be sanitized in-place")}}else if(i instanceof ue)t=Qe("<!---->"),o=t.ownerDocument.importNode(i,!0),o.nodeType===J.element&&o.nodeName==="BODY"||o.nodeName==="HTML"?t=o:t.appendChild(o);else{if(!k&&!P&&!w&&i.indexOf("<")===-1)return g&&oe?g.createHTML(i):i;if(t=Qe(i),!t)return k?null:oe?B:""}t&&Ae&&b(t.firstChild);const u=et(X?i:t);for(;c=u.nextNode();)nt(c),at(c),c.content instanceof D&&jt(c.content);if(X)return i;if(k){if(ne)for(_=wt.call(t.ownerDocument);t.firstChild;)_.appendChild(t.firstChild);else _=t;return(d.shadowroot||d.shadowrootmode)&&(_=Pt.call(l,_,!0)),_}let O=w?t.outerHTML:t.innerHTML;return w&&m["!doctype"]&&t.ownerDocument&&t.ownerDocument.doctype&&t.ownerDocument.doctype.name&&h(Rt,t.ownerDocument.doctype.name)&&(O="<!DOCTYPE "+t.ownerDocument.doctype.name+`>
`+O),P&&le([pe,de,Te],M=>{O=q(O,M," ")}),g&&oe?g.createHTML(O):O},n.setConfig=function(){let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};ye(i),ge=!0},n.clearConfig=function(){H=null,ge=!1},n.isValidAttribute=function(i,e,t){H||ye({});const o=p(i),c=p(e);return ot(o,c,t)},n.addHook=function(i,e){typeof e=="function"&&$(A[i],e)},n.removeHook=function(i,e){if(e!==void 0){const t=Jt(A[i],e);return t===-1?void 0:Qt(A[i],t,1)[0]}return ft(A[i])},n.removeHooks=function(i){A[i]=[]},n.removeAllHooks=function(){A=Et()},n}var ke=Ot();const En=Vt("div",{target:"ez8s6cm0"})({width:"100%"}),Pe="data-temp-href-target";ke.addHook("beforeSanitizeAttributes",function(a){a instanceof HTMLElement&&a.hasAttribute("target")&&a.getAttribute("target")==="_blank"&&a.setAttribute(Pe,"_blank")});ke.addHook("afterSanitizeAttributes",function(a){a instanceof HTMLElement&&a.hasAttribute(Pe)&&(a.setAttribute("target","_blank"),a.setAttribute("rel","noopener noreferrer"),a.removeAttribute(Pe))});const gn=a=>{const n={USE_PROFILES:{html:!0},FORCE_BODY:!0};return ke.sanitize(a,n)};function An({element:a}){const{body:n}=a,s=gt.useMemo(()=>gn(n),[n]);return lt($t,{children:s&&lt(En,{className:"stHtml","data-testid":"stHtml",dangerouslySetInnerHTML:{__html:s}})})}const Sn=gt.memo(An);export{Sn as default};
