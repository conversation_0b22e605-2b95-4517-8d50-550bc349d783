import{n as e,r as a,C as t,j as r,aC as o}from"./index.C1NIn1Y2.js";import{P as i}from"./ProgressBar.C0zPMe-p.js";const l=e("div",{target:"e1675qd10"})(({theme:s})=>({paddingBottom:s.spacing.sm,lineHeight:"normal"}));function n({element:s}){return t("div",{className:"stProgress","data-testid":"stProgress",children:[r(l,{children:r(o,{source:s.text,allowHTML:!1,isLabel:!0})}),r(i,{value:s.value})]})}const m=a.memo(n);export{m as default};
