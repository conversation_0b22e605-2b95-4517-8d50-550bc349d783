# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/MultiSelect.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import LabelVisibilityMessage_pb2 as streamlit_dot_proto_dot_LabelVisibilityMessage__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!streamlit/proto/MultiSelect.proto\x1a,streamlit/proto/LabelVisibilityMessage.proto\"\xcd\x02\n\x0bMultiSelect\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05label\x18\x02 \x01(\t\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\x03 \x03(\x05\x12\x0f\n\x07options\x18\x04 \x03(\t\x12\x0c\n\x04help\x18\x05 \x01(\t\x12\x0f\n\x07\x66orm_id\x18\x06 \x01(\t\x12\x11\n\x05value\x18\x07 \x03(\x05\x42\x02\x18\x01\x12\x12\n\nraw_values\x18\x0e \x03(\t\x12\x11\n\tset_value\x18\x08 \x01(\x08\x12\x10\n\x08\x64isabled\x18\t \x01(\x08\x12\x31\n\x10label_visibility\x18\n \x01(\x0b\x32\x17.LabelVisibilityMessage\x12\x16\n\x0emax_selections\x18\x0b \x01(\x05\x12\x13\n\x0bplaceholder\x18\x0c \x01(\t\x12\x1f\n\x12\x61\x63\x63\x65pt_new_options\x18\r \x01(\x08H\x00\x88\x01\x01\x42\x15\n\x13_accept_new_optionsB0\n\x1c\x63om.snowflake.apps.streamlitB\x10MultiSelectProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.MultiSelect_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\020MultiSelectProto'
  _globals['_MULTISELECT'].fields_by_name['value']._loaded_options = None
  _globals['_MULTISELECT'].fields_by_name['value']._serialized_options = b'\030\001'
  _globals['_MULTISELECT']._serialized_start=84
  _globals['_MULTISELECT']._serialized_end=417
# @@protoc_insertion_point(module_scope)
