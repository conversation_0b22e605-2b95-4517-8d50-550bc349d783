"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class Skeleton(google.protobuf.message.Message):
    """An empty-like element that displays an app skeleton."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _SkeletonStyle:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _SkeletonStyleEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[Skeleton._SkeletonStyle.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        ELEMENT: Skeleton._SkeletonStyle.ValueType  # 0
        APP: Skeleton._SkeletonStyle.ValueType  # 1
        """internal-only, for now"""

    class SkeletonStyle(_SkeletonStyle, metaclass=_SkeletonStyleEnumTypeWrapper): ...
    ELEMENT: Skeleton.SkeletonStyle.ValueType  # 0
    APP: Skeleton.SkeletonStyle.ValueType  # 1
    """internal-only, for now"""

    STYLE_FIELD_NUMBER: builtins.int
    HEIGHT_FIELD_NUMBER: builtins.int
    style: global___Skeleton.SkeletonStyle.ValueType
    """Skeleton visual style"""
    height: builtins.int
    """Height in pixels"""
    def __init__(
        self,
        *,
        style: global___Skeleton.SkeletonStyle.ValueType = ...,
        height: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_height", b"_height", "height", b"height"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_height", b"_height", "height", b"height", "style", b"style"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_height", b"_height"]) -> typing.Literal["height"] | None: ...

global___Skeleton = Skeleton
