"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class LabelVisibilityMessage(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _LabelVisibilityOptions:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _LabelVisibilityOptionsEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[LabelVisibilityMessage._LabelVisibilityOptions.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        VISIBLE: LabelVisibilityMessage._LabelVisibilityOptions.ValueType  # 0
        HIDDEN: LabelVisibilityMessage._LabelVisibilityOptions.ValueType  # 1
        COLLAPSED: LabelVisibilityMessage._LabelVisibilityOptions.ValueType  # 2

    class LabelVisibilityOptions(_LabelVisibilityOptions, metaclass=_LabelVisibilityOptionsEnumTypeWrapper):
        """We use separate LabelVisibilityMessage instead of just defining Enum and
        use it in other widgets proto files due to protobuf js error, when just
        enum defined and imported
        https://github.com/protobufjs/protobuf.js/issues/1414
        """

    VISIBLE: LabelVisibilityMessage.LabelVisibilityOptions.ValueType  # 0
    HIDDEN: LabelVisibilityMessage.LabelVisibilityOptions.ValueType  # 1
    COLLAPSED: LabelVisibilityMessage.LabelVisibilityOptions.ValueType  # 2

    VALUE_FIELD_NUMBER: builtins.int
    value: global___LabelVisibilityMessage.LabelVisibilityOptions.ValueType
    def __init__(
        self,
        *,
        value: global___LabelVisibilityMessage.LabelVisibilityOptions.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["value", b"value"]) -> None: ...

global___LabelVisibilityMessage = LabelVisibilityMessage
