/* Healthcare Operations Assistant Custom Styles */

/* Layout and Structure */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

/* Chat Interface */
.chat-container {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1rem;
    margin: 1rem 0;
}

.user-message {
    background-color: #e3f2fd;
    padding: 1rem;
    border-radius: 10px;
    margin: 1rem 0;
    border-left: 5px solid #1976d2;
}

.assistant-message {
    background-color: #f5f5f5;
    padding: 1rem;
    border-radius: 10px;
    margin: 1rem 0;
    border-left: 5px solid #4caf50;
}

.message-timestamp {
    font-size: 0.8rem;
    color: #707070;
    margin-top: 0.25rem;
}

/* Metrics Dashboard */
.metric-card {
    background-color: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.metric-title {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1976d2;
}

.metric-trend {
    font-size: 0.9rem;
    color: #4caf50;
}

.metric-trend.negative {
    color: #f44336;
}

/* Header Styling */
.header {
    background-color: white;
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 2rem;
}

.header-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
}

.header-subtitle {
    font-size: 1rem;
    color: #707070;
}

/* Sidebar Styling */
.sidebar {
    background-color: white;
    padding: 1.5rem;
    border-right: 1px solid #e0e0e0;
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-normal {
    background-color: #4caf50;
    color: white;
}

.status-warning {
    background-color: #ff9800;
    color: white;
}

.status-critical {
    background-color: #f44336;
    color: white;
}

/* Buttons and Interactive Elements */
.action-button {
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.action-button:hover {
    background-color: #1976d2;
}

.action-button.secondary {
    background-color: #f5f5f5;
    color: #2c3e50;
    border: 1px solid #e0e0e0;
}

.action-button.secondary:hover {
    background-color: #e0e0e0;
}

/* Notifications */
.notification {
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.notification.info {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.notification.success {
    background-color: #e8f5e9;
    border-left: 4px solid #4caf50;
}

.notification.warning {
    background-color: #fff3e0;
    border-left: 4px solid #ff9800;
}

.notification.error {
    background-color: #ffebee;
    border-left: 4px solid #f44336;
}

/* Responsive Design */
@media (max-width: 768px) {
    .metric-card {
        margin-bottom: 1rem;
    }
    
    .header-title {
        font-size: 1.5rem;
    }
    
    .sidebar {
        padding: 1rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes slideIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}