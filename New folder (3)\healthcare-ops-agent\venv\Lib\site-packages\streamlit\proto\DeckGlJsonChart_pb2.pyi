"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class DeckGlJsonChart(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _SelectionMode:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _SelectionModeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[DeckGlJsonChart._SelectionMode.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        SINGLE_OBJECT: DeckGlJsonChart._SelectionMode.ValueType  # 0
        """Only one object can be selected at a time."""
        MULTI_OBJECT: DeckGlJsonChart._SelectionMode.ValueType  # 1
        """Multiple objects can be selected at a time."""

    class SelectionMode(_SelectionMode, metaclass=_SelectionModeEnumTypeWrapper):
        """Available selection modes:"""

    SINGLE_OBJECT: DeckGlJsonChart.SelectionMode.ValueType  # 0
    """Only one object can be selected at a time."""
    MULTI_OBJECT: DeckGlJsonChart.SelectionMode.ValueType  # 1
    """Multiple objects can be selected at a time."""

    JSON_FIELD_NUMBER: builtins.int
    TOOLTIP_FIELD_NUMBER: builtins.int
    USE_CONTAINER_WIDTH_FIELD_NUMBER: builtins.int
    ID_FIELD_NUMBER: builtins.int
    MAPBOX_TOKEN_FIELD_NUMBER: builtins.int
    WIDTH_FIELD_NUMBER: builtins.int
    HEIGHT_FIELD_NUMBER: builtins.int
    SELECTION_MODE_FIELD_NUMBER: builtins.int
    FORM_ID_FIELD_NUMBER: builtins.int
    json: builtins.str
    """The json of the pydeck object (https://deckgl.readthedocs.io/en/latest/deck.html)"""
    tooltip: builtins.str
    use_container_width: builtins.bool
    """If True, will overwrite the chart width spec to fit to container."""
    id: builtins.str
    """ID, required for selection events."""
    mapbox_token: builtins.str
    """The Mapbox token, if any."""
    width: builtins.int
    """Width in pixels"""
    height: builtins.int
    """Height in pixels"""
    form_id: builtins.str
    """The form ID of the widget, this is required if the chart has selection events"""
    @property
    def selection_mode(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[global___DeckGlJsonChart.SelectionMode.ValueType]:
        """If non-empty, treat this instance as a Widget"""

    def __init__(
        self,
        *,
        json: builtins.str = ...,
        tooltip: builtins.str = ...,
        use_container_width: builtins.bool = ...,
        id: builtins.str = ...,
        mapbox_token: builtins.str = ...,
        width: builtins.int = ...,
        height: builtins.int = ...,
        selection_mode: collections.abc.Iterable[global___DeckGlJsonChart.SelectionMode.ValueType] | None = ...,
        form_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["form_id", b"form_id", "height", b"height", "id", b"id", "json", b"json", "mapbox_token", b"mapbox_token", "selection_mode", b"selection_mode", "tooltip", b"tooltip", "use_container_width", b"use_container_width", "width", b"width"]) -> None: ...

global___DeckGlJsonChart = DeckGlJsonChart
